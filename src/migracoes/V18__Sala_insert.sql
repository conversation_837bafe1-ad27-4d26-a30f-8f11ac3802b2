INSERT INTO Sala (id_andar, nome, descricao) VALUES
(11, 'Sala Central', 'Uma sala gigantesca e gelada com servidores.'),
(11, 'Data Center', 'Fileiras de servidores zumbindo.'),
(11, 'Sala de Controle', 'Monitores mostrando métricas dos servidores.'),
(12, 'Sala Central', 'Área principal do almoxarifado.'),
(12, 'Depósito', 'Prateleiras com equipamentos.'),
(12, 'Oficina', 'Bancadas para manutenção.'),
(13, 'Sala Central', 'Área principal da recepção.'),
(13, 'Recepção', 'Balcão com recepcionista.'),
(13, 'Área de Espera', 'Sofás e revistas tech.'),
(1, 'Sala Central', 'Central do suporte básico.'),
(1, 'Help Desk', 'Baias de atendimento.'),
(1, 'Sala de Treinamento', 'Sala com computadores em fileiras.'),
(2, 'Sala Central', 'Centro da oficina de hardware.'),
(2, 'Laboratório', 'Bancadas com equipamentos.'),
(2, 'Depósito de Peças', 'Estoque organizado.'),
(3, 'Sala Central', 'Central de suporte remoto.'),
(3, 'NOC', 'Sala de monitoramento.'),
(3, 'Sala de Atendimento', 'Estações de trabalho.'),
(4, 'Sala Central', 'Centro de infraestrutura.'),
(4, 'Sala de Servidores', 'Racks de equipamentos.'),
(4, 'Sala de Redes', 'Central de cabeamento.'),
(5, 'Sala Central', 'Centro de operações de rede.'),
(5, 'Sala de Monitoramento', 'Telões com métricas.'),
(5, 'Laboratório de Redes', 'Área de testes.'),
(6, 'Sala Central', 'Centro de segurança.'),
(6, 'SOC', 'Centro de operações de segurança.'),
(6, 'Sala de Testes', 'Ambiente isolado para testes.'),
(7, 'Sala Central', 'Área de desenvolvimento web.'),
(7, 'Frontend', 'Time de frontend.'),
(7, 'Design', 'Equipe de UX/UI.'),
(8, 'Sala Central', 'Área de backend.'),
(8, 'Desenvolvimento', 'Time de backend.'),
(8, 'Arquitetura', 'Planejamento de sistemas.'),
(9, 'Sala Central', 'Centro de DevOps.'),
(9, 'CI/CD', 'Pipelines e automação.'),
(9, 'Containers', 'Orquestração e deploy.'),
(10, 'Sala Central', 'Recepção da diretoria.'),
(10, 'Sala do Chefe', 'Escritório principal.'),
(10, 'Sala de Reuniões', 'Mesa grande e tela de projeção.');


INSERT INTO ConexaoSala (id_sala_origem, id_sala_destino) VALUES
--subsolo 2
(1, 2), 
(2, 1), 
(1, 3), 
(3, 1),
--subsolo 1 
(4, 5), 
(5, 4),
(4, 6), 
(6, 4), 
--térreo 
(7, 8),
(8, 7), 
(7, 9), 
(9, 7), 
--andar 1 
(10, 11), 
(11, 10), 
(10, 12), 
(12, 10), 
--andar 2 
(13, 14), 
(14, 13),
(13, 15), 
(15, 13), 
--andar 3 
(16, 17), 
(17, 16), 
(16, 18), 
(18, 16), 
--andar 4 
(19, 20),
(20, 19), 
(19, 21), 
(21, 19), 
--andar 5 
(22, 23),
(23, 22), 
(22, 24), 
(24, 22), 
--andar 6 
(25, 26),
(26, 25), 
(25, 27), 
(27, 25), 
--andar 7
(28, 29), 
(29, 28), 
(28, 30), 
(30, 28), 
--andar 8
(31, 32), 
(32, 31),
(31, 33), 
(33, 31), 
--andar 9
(34, 35), 
(35, 34), 
(34, 36), 
(36, 34), 
--andar 10 
(37, 38), 
(38, 37), 
(37, 39), 
(39, 37); 

INSERT INTO PC (id_pc, id_sala) VALUES
(1, 3),
(2, 1),
(3, 11),
(4, 12),
(5, 35),
(6, 32),
(7, 17),
(8, 38);