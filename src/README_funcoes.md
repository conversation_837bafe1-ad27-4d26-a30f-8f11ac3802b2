# Sistema de Inimigos - Funcionalidades

Este sistema implementa a geração e gerenciamento de inimigos em PCs conforme solicitado.

## Características Principais

### 1. Tipos de Inimigos e Chances de Spawn
- **Bug**: 50% de chance de spawnar
- **Malware**: 40% de chance de spawnar  
- **Hacker**: 10% de chance de spawnar

### 2. Sistema de Spawn Automático
- Inimigos spawnam automaticamente a cada 5 minutos em PCs normais
- O sistema calcula quantos períodos de 5 minutos se passaram desde o último acesso
- Gera um inimigo para cada período

### 3. PC Especial
- PC com ID 99 (configurável)
- Não gera inimigos automaticamente
- Quando você escolhe "limpar PC", gera entre 1 a 8 inimigos aleatórios instantaneamente

## Arquivos

### `funcoes.py`
Arquivo principal com todas as funções do sistema:

#### Funções Principais:
- `inicializar_sistema()`: Configura o banco de dados e cria estruturas necessárias
- `acessar_pc(id_pc)`: Função principal para acessar um PC
- `verificar_inimigos_no_pc(id_pc)`: Verifica se há inimigos em um PC
- `gerar_inimigos_automaticos(id_pc)`: Gera inimigos baseado no tempo (PCs normais)
- `limpar_pc_especial()`: Gera 1-8 inimigos aleatórios no PC especial
- `gerar_inimigo_no_pc(id_pc, id_inimigo=None)`: Gera um inimigo específico ou aleatório
- `remover_inimigo(id_instancia)`: Remove um inimigo após combate
- `limpar_todos_inimigos_pc(id_pc)`: Remove todos os inimigos de um PC

#### Funções de Configuração:
- `criar_pc_especial()`: Cria o PC especial no banco
- `adicionar_coluna_ultimo_acesso()`: Adiciona coluna de controle de tempo
- `adicionar_coluna_pc_instancia_inimigo()`: Adiciona relação PC-Inimigo

### `exemplo_uso_funcoes.py`
Arquivo de demonstração e teste com menu interativo.

## Como Usar

### 1. Inicialização
```python
from funcoes import inicializar_sistema
inicializar_sistema()
```

### 2. Acessar PC Normal
```python
from funcoes import acessar_pc
acessar_pc(1)  # Acessa PC ID 1
```

### 3. Acessar PC Especial
```python
from funcoes import acessar_pc, PC_ESPECIAL_ID
acessar_pc(PC_ESPECIAL_ID)  # Acessa PC especial
```

### 4. Verificar Inimigos
```python
from funcoes import verificar_inimigos_no_pc
verificar_inimigos_no_pc(1)  # Verifica inimigos no PC 1
```

### 5. Gerar Inimigo Específico
```python
from funcoes import gerar_inimigo_no_pc
gerar_inimigo_no_pc(1, 1)  # Gera um Bug no PC 1
gerar_inimigo_no_pc(1)     # Gera inimigo aleatório no PC 1
```

## Configurações

### Modificar Chances de Spawn
Edite o dicionário `INIMIGOS_CONFIG` em `funcoes.py`:

```python
INIMIGOS_CONFIG = {
    1: {'nome': 'Bug', 'chance': 50, 'vida_min': 80, 'vida_max': 120, 'velocidade_min': 180, 'velocidade_max': 220},
    2: {'nome': 'Malware', 'chance': 40, 'vida_min': 400, 'vida_max': 600, 'velocidade_min': 80, 'velocidade_max': 120},
    3: {'nome': 'Hacker', 'chance': 10, 'vida_min': 800, 'vida_max': 1000, 'velocidade_min': 50, 'velocidade_max': 90}
}
```

### Modificar ID do PC Especial
Altere a constante `PC_ESPECIAL_ID` em `funcoes.py`:

```python
PC_ESPECIAL_ID = 99  # Altere para o ID desejado
```

### Modificar Tempo de Spawn
Para alterar o intervalo de 5 minutos, modifique a linha na função `gerar_inimigos_automaticos()`:

```python
periodos_5min = int(minutos_passados // 5)  # Altere o 5 para outro valor
```

## Estrutura do Banco de Dados

### Modificações Necessárias
O sistema adiciona automaticamente:

1. **Coluna `ultimo_acesso` na tabela PC**:
   ```sql
   ALTER TABLE PC ADD COLUMN ultimo_acesso TIMESTAMP;
   ```

2. **Coluna `id_pc` na tabela InstanciaInimigo**:
   ```sql
   ALTER TABLE InstanciaInimigo ADD COLUMN id_pc INT REFERENCES PC(id_pc);
   ```

3. **PC Especial**:
   ```sql
   INSERT INTO PC (id_pc, id_sala) VALUES (99, 1);
   ```

## Exemplo de Fluxo de Uso

1. **Inicializar o sistema**:
   ```python
   inicializar_sistema()
   ```

2. **Jogador acessa PC normal após 10 minutos**:
   ```python
   acessar_pc(1)
   # Saída: "Gerando 2 inimigo(s) no PC #1..." (10 min ÷ 5 min = 2 inimigos)
   ```

3. **Jogador acessa PC especial**:
   ```python
   acessar_pc(99)
   # Menu: 1. Verificar inimigos, 2. Limpar PC, 3. Sair
   # Escolhe opção 2: gera 1-8 inimigos aleatórios
   ```

4. **Verificar inimigos antes do combate**:
   ```python
   verificar_inimigos_no_pc(1)
   # Lista todos os inimigos com seus atributos
   ```

5. **Após combate, remover inimigo derrotado**:
   ```python
   remover_inimigo(123)  # Remove inimigo com ID de instância 123
   ```

## Teste do Sistema

Execute o arquivo de exemplo:
```bash
python src/exemplo_uso_funcoes.py
```

Escolha a opção 2 para uma demonstração automática completa.

## Observações

- O sistema é compatível com a estrutura existente do banco de dados
- Todas as modificações são feitas de forma segura (verifica se já existem antes de criar)
- Os atributos dos inimigos (vida, velocidade) são gerados aleatoriamente dentro dos ranges configurados
- O sistema mantém compatibilidade com o código existente em `interacoes.py`
