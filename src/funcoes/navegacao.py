from database import get_location_details, get_pcs_in_room, get_sala_id, call_db_function, get_connection
from .pc_interacao import acessar_pc_menu
from .inimigos import listar_pcs_com_problemas

def exibir_local(personagem_id, personagem_nome):
    location_details = get_location_details(personagem_id)
    if not location_details:
        print("Erro ao carregar o local. Voltando ao menu.")
        return False
    nome_local, descricao, _ = location_details
    print(f"--- {personagem_nome} ---")
    print(f"Você está no: {nome_local}")
    print(descricao)
    print("\n--------------------")
    print("O que você faz?\n")
    return True

def verificar_npc_na_sala(sala_id):
    # Verifica se há NPCs na sala atual
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT n.nome, n.tipo
            FROM npc n
            WHERE n.sala_atual = %s;
        """, (sala_id,))
        npcs = cur.fetchall()
    conn.close()
    return npcs

def montar_opcoes(personagem_id):
    # função que mostra todas as opcoes na tela
    location_details = get_location_details(personagem_id)
    saidas_disponiveis = location_details[2] if location_details else []
    sala_id = get_sala_id(personagem_id)
    pcs = get_pcs_in_room(sala_id)
    npcs = verificar_npc_na_sala(sala_id)

    opcoes = []
    for saida in saidas_disponiveis:
        opcoes.append({'tipo': 'saida', 'nome': saida})
    if pcs:
        opcoes.append({'tipo': 'pc', 'nome': 'Acessar PC'})
    if npcs:
        for nome_npc, tipo_npc in npcs:
            opcoes.append({'tipo': 'npc', 'nome': f'Falar com {nome_npc}', 'npc_nome': nome_npc, 'npc_tipo': tipo_npc})
    opcoes.append({'tipo': 'sair', 'nome': 'Voltar ao menu principal'})
    return opcoes, saidas_disponiveis

def falar_com_npc():
    if opcao['npc_tipo'] == 'recepcionista':

        print("\n--- RECEPCIONISTA ---")
        print("Recepcionista: Olá estagiário, outros funcionários relataram um problema para você resolver.")
        pcs_com_problemas = listar_pcs_com_problemas()

        if pcs_com_problemas:
            print("Recepcionista: Olá estagiário, outros funcionários relataram um problema para você resolver.")
            print(f"Recepcionista: Os seguintes PCs estão com problemas: {', '.join(f'PC #{pc}' for pc in pcs_com_problemas)}")
        else:
            print("Recepcionista: No momento, todos os PCs estão funcionando normalmente!")

        print("Recepcionista: Boa sorte com os reparos!")
        print("--------------------")
    else:
        print(f"\n{opcao['npc_nome']}: Olá! Como posso ajudar?")
        input("\nPressione Enter para continuar...")
        return True

def exibir_opcoes(opcoes):
    for idx, opcao in enumerate(opcoes, start=1):
        print(f"  [{idx}] {opcao['nome']}")
    print("\n--------------------")

def processar_escolha(personagem_id, opcoes, saidas):
    escolha_str = input("Sua escolha: ").strip()
    if not escolha_str:
        return True
    escolha_num = int(escolha_str)
    if not (1 <= escolha_num <= len(opcoes)):
        print("\nOpção inválida. Tente novamente.")
        return True
    opcao = opcoes[escolha_num - 1]
    if opcao['tipo'] == 'saida':
        direcao_escolhida = opcao['nome']
        print(f"\nTentando: {direcao_escolhida}...")
        call_db_function('mover_personagem', personagem_id, direcao_escolhida)
        return True
    elif opcao['tipo'] == 'pc':
        acessar_pc_menu(personagem_id)
        input("\nPressione Enter para continuar...")
        return True
    elif opcao['tipo'] == 'npc':
        falar_com_npc()
    elif opcao['tipo'] == 'sair':
        print("\nVoltando ao menu principal...")
        return False