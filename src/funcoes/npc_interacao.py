from database import call_db_function, get_connection, get_dialogo_npc

def falar_com_npc(npc_nome):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("SELECT id_npc FROM NPC WHERE nome = %s;", (npc_nome,))
        row = cur.fetchone()
    conn.close()
    if row:
        id_npc = row[0]
        dialogo = get_dialogo_npc(id_npc)
        print(f"\n{npc_nome}: {dialogo}")
    else:
        print("NPC não encontrado.")