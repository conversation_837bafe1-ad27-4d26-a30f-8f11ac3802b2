import random
from datetime import datetime, timedelta
from database import get_connection

PCS_BUGADOS = [9, 10]
LIMITE_INIMIGOS_PC = 8

INIMIGOS_CONFIG = {
    1: {'nome': 'Bug', 'vida_min': 80, 'vida_max': 120, 'velocidade_min': 180, 'velocidade_max': 220},
    2: {'nome': 'Malware', 'vida_min': 400, 'vida_max': 600, 'velocidade_min': 80, 'velocidade_max': 120},
    3: {'nome': 'Hacker', 'vida_min': 800, 'vida_max': 1000, 'velocidade_min': 50, 'velocidade_max': 90}
}

def escolher_inimigo_aleatorio():
    return random.choice(list(INIMIGOS_CONFIG.keys()))

def contar_inimigos_no_pc(id_pc):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("SELECT COUNT(*) FROM instanciainimigo WHERE id_pc = %s;", (id_pc,))
        return cur.fetchone()[0]
    conn.close()

def pc_esta_inutilizavel(id_pc):
    return contar_inimigos_no_pc(id_pc) >= LIMITE_INIMIGOS_PC

def gerar_inimigo_no_pc(id_pc):
    id_inimigo = escolher_inimigo_aleatorio()
    config = INIMIGOS_CONFIG[id_inimigo]
    vida = random.randint(config['vida_min'], config['vida_max'])
    velocidade = random.randint(config['velocidade_min'], config['velocidade_max'])
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute(
            "INSERT INTO instanciainimigo (id_inimigo, vida, velocidade, id_pc) VALUES (%s, %s, %s, %s);",
            (id_inimigo, vida, velocidade, id_pc)
        )
        conn.commit()
    conn.close()

def gerar_inimigo_se_5min_passaram():
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("SELECT id_pc, ultimo_acesso FROM pc WHERE id_pc NOT IN %s;", (tuple(PCS_BUGADOS),))
        pcs = cur.fetchall()
        agora = datetime.now()
        for id_pc, ultimo_acesso in pcs:
            if not ultimo_acesso or (agora - ultimo_acesso) >= timedelta(minutes=5):
                gerar_inimigo_no_pc(id_pc)
                cur.execute("UPDATE pc SET ultimo_acesso = %s WHERE id_pc = %s;", (agora, id_pc))
        conn.commit()
    conn.close()   