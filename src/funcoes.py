import random
import time
from datetime import datetime, timedelta
from database import get_connection

# Configurações dos inimigos e suas chances de spawn
INIMIGOS_CONFIG = {
    1: {'nome': 'Bug', 'chance': 50, 'vida_min': 80, 'vida_max': 120, 'velocidade_min': 180, 'velocidade_max': 220},
    2: {'nome': 'Malware', 'chance': 40, 'vida_min': 400, 'vida_max': 600, 'velocidade_min': 80, 'velocidade_max': 120},
    3: {'nome': 'Hacker', 'chance': 10, 'vida_min': 800, 'vida_max': 1000, 'velocidade_min': 50, 'velocidade_max': 90}
}

# ID do PC especial que sempre gera inimigos quando acessado
PC_ESPECIAL_ID = 99  # Você pode alterar este ID conforme necessário

def criar_pc_especial():
    """Cria o PC especial no banco de dados se não existir"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            # Verifica se o PC especial já existe
            cur.execute("SELECT id_pc FROM PC WHERE id_pc = %s;", (PC_ESPECIAL_ID,))
            if cur.fetchone():
                print(f"PC especial #{PC_ESPECIAL_ID} já existe.")
                return True
            
            # Busca uma sala disponível para colocar o PC especial (vamos usar a sala 1)
            cur.execute("SELECT id_sala FROM Sala LIMIT 1;")
            sala_result = cur.fetchone()
            if not sala_result:
                print("Erro: Nenhuma sala encontrada no banco de dados.")
                return False
            
            id_sala = sala_result[0]
            
            # Cria o PC especial
            cur.execute("INSERT INTO PC (id_pc, id_sala) VALUES (%s, %s);", (PC_ESPECIAL_ID, id_sala))
            conn.commit()
            print(f"PC especial #{PC_ESPECIAL_ID} criado com sucesso!")
            return True
            
    except Exception as e:
        print(f"Erro ao criar PC especial: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def adicionar_coluna_ultimo_acesso():
    """Adiciona a coluna ultimo_acesso na tabela PC se não existir"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            # Verifica se a coluna já existe
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'pc' AND column_name = 'ultimo_acesso';
            """)
            
            if not cur.fetchone():
                # Adiciona a coluna se não existir
                cur.execute("ALTER TABLE PC ADD COLUMN ultimo_acesso TIMESTAMP;")
                conn.commit()
                print("Coluna 'ultimo_acesso' adicionada à tabela PC.")
            else:
                print("Coluna 'ultimo_acesso' já existe na tabela PC.")
            return True
            
    except Exception as e:
        print(f"Erro ao adicionar coluna ultimo_acesso: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def adicionar_coluna_pc_instancia_inimigo():
    """Adiciona a coluna id_pc na tabela InstanciaInimigo se não existir"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            # Verifica se a coluna já existe
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'instanciainimigo' AND column_name = 'id_pc';
            """)
            
            if not cur.fetchone():
                # Adiciona a coluna se não existir
                cur.execute("ALTER TABLE InstanciaInimigo ADD COLUMN id_pc INT REFERENCES PC(id_pc);")
                conn.commit()
                print("Coluna 'id_pc' adicionada à tabela InstanciaInimigo.")
            else:
                print("Coluna 'id_pc' já existe na tabela InstanciaInimigo.")
            return True
            
    except Exception as e:
        print(f"Erro ao adicionar coluna id_pc: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def escolher_inimigo_aleatorio():
    """Escolhe um tipo de inimigo baseado nas chances percentuais"""
    total_chance = sum(config['chance'] for config in INIMIGOS_CONFIG.values())
    numero_aleatorio = random.randint(1, total_chance)
    
    chance_acumulada = 0
    for id_inimigo, config in INIMIGOS_CONFIG.items():
        chance_acumulada += config['chance']
        if numero_aleatorio <= chance_acumulada:
            return id_inimigo
    
    # Fallback para Bug se algo der errado
    return 1

def gerar_inimigo_no_pc(id_pc, id_inimigo=None):
    """Gera um inimigo específico ou aleatório em um PC"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            # Se não especificou o inimigo, escolhe aleatoriamente
            if id_inimigo is None:
                id_inimigo = escolher_inimigo_aleatorio()
            
            # Busca as configurações do inimigo
            if id_inimigo not in INIMIGOS_CONFIG:
                print(f"Erro: Inimigo com ID {id_inimigo} não encontrado.")
                return False
            
            config = INIMIGOS_CONFIG[id_inimigo]
            
            # Gera atributos aleatórios baseados na configuração
            vida = random.randint(config['vida_min'], config['vida_max'])
            velocidade = random.randint(config['velocidade_min'], config['velocidade_max'])
            
            # Insere o inimigo no banco
            cur.execute("""
                INSERT INTO InstanciaInimigo (id_inimigo, vida, velocidade, id_pc) 
                VALUES (%s, %s, %s, %s);
            """, (id_inimigo, vida, velocidade, id_pc))
            
            conn.commit()
            print(f"Um {config['nome']} apareceu no PC #{id_pc}! (Vida: {vida}, Velocidade: {velocidade})")
            return True
            
    except Exception as e:
        print(f"Erro ao gerar inimigo: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def gerar_inimigos_automaticos(id_pc):
    """Gera inimigos automaticamente baseado no tempo desde o último acesso (a cada 5 minutos)"""
    if id_pc == PC_ESPECIAL_ID:
        print("Este é o PC especial. Use a função 'limpar_pc' para gerar inimigos.")
        return
    
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return
    
    try:
        with conn.cursor() as cur:
            # Busca o último acesso do PC
            cur.execute("SELECT ultimo_acesso FROM PC WHERE id_pc = %s;", (id_pc,))
            row = cur.fetchone()
            
            if not row:
                print(f"PC #{id_pc} não encontrado.")
                return
            
            ultimo_acesso = row[0]
            agora = datetime.now()
            
            # Se nunca foi acessado, considera como se fosse há 5 minutos
            if not ultimo_acesso:
                ultimo_acesso = agora - timedelta(minutes=5)
            
            # Calcula quantos períodos de 5 minutos se passaram
            minutos_passados = (agora - ultimo_acesso).total_seconds() / 60
            periodos_5min = int(minutos_passados // 5)
            
            if periodos_5min > 0:
                print(f"Gerando {periodos_5min} inimigo(s) no PC #{id_pc}...")
                
                # Gera um inimigo para cada período de 5 minutos
                for _ in range(periodos_5min):
                    gerar_inimigo_no_pc(id_pc)
                
                # Atualiza o último acesso
                cur.execute("UPDATE PC SET ultimo_acesso = %s WHERE id_pc = %s;", (agora, id_pc))
                conn.commit()
                
            else:
                print("Nenhum novo inimigo apareceu desde o último acesso.")
                
    except Exception as e:
        print(f"Erro ao gerar inimigos automáticos: {e}")
        conn.rollback()
    finally:
        conn.close()

def verificar_inimigos_no_pc(id_pc):
    """Verifica se há inimigos em um PC específico"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return []
    
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT ii.id_instancia, i.nome, ii.vida, ii.velocidade
                FROM InstanciaInimigo ii
                JOIN Inimigo i ON ii.id_inimigo = i.id_inimigo
                WHERE ii.id_pc = %s;
            """, (id_pc,))
            
            inimigos = cur.fetchall()
            
            if inimigos:
                print(f"\n=== INIMIGOS NO PC #{id_pc} ===")
                for id_instancia, nome, vida, velocidade in inimigos:
                    print(f"- {nome} (ID: {id_instancia}) - Vida: {vida}, Velocidade: {velocidade}")
                print(f"Total: {len(inimigos)} inimigo(s)")
            else:
                print(f"Nenhum inimigo encontrado no PC #{id_pc}.")
            
            return inimigos
            
    except Exception as e:
        print(f"Erro ao verificar inimigos: {e}")
        return []
    finally:
        conn.close()

def limpar_pc_especial():
    """Gera um grupo aleatório de 1 a 8 inimigos no PC especial"""
    print(f"\n=== LIMPANDO PC ESPECIAL #{PC_ESPECIAL_ID} ===")
    
    # Gera entre 1 e 8 inimigos
    quantidade_inimigos = random.randint(1, 8)
    print(f"Gerando {quantidade_inimigos} inimigo(s) para combate...")
    
    inimigos_gerados = []
    for i in range(quantidade_inimigos):
        if gerar_inimigo_no_pc(PC_ESPECIAL_ID):
            inimigos_gerados.append(i + 1)
        time.sleep(0.1)  # Pequena pausa para não sobrecarregar
    
    print(f"\n{len(inimigos_gerados)} inimigo(s) gerado(s) com sucesso!")
    print("Prepare-se para o combate!")
    
    return len(inimigos_gerados)

def acessar_pc(id_pc):
    """Função principal para acessar um PC"""
    print(f"\n=== ACESSANDO PC #{id_pc} ===")
    
    # Se for o PC especial, não gera inimigos automaticamente
    if id_pc == PC_ESPECIAL_ID:
        print("Este é o PC especial de treinamento!")
        print("Escolha uma opção:")
        print("1. Verificar inimigos atuais")
        print("2. Limpar PC (gerar novos inimigos)")
        print("3. Sair")
        
        try:
            opcao = int(input("Sua escolha: ").strip())
            if opcao == 1:
                verificar_inimigos_no_pc(id_pc)
            elif opcao == 2:
                limpar_pc_especial()
            elif opcao == 3:
                print("Saindo do PC especial...")
            else:
                print("Opção inválida.")
        except ValueError:
            print("Digite um número válido.")
    else:
        # PC normal - gera inimigos baseado no tempo
        gerar_inimigos_automaticos(id_pc)
        verificar_inimigos_no_pc(id_pc)

def inicializar_sistema():
    """Inicializa o sistema criando as estruturas necessárias"""
    print("Inicializando sistema de inimigos...")
    
    # Adiciona colunas necessárias se não existirem
    adicionar_coluna_ultimo_acesso()
    adicionar_coluna_pc_instancia_inimigo()
    
    # Cria o PC especial se não existir
    criar_pc_especial()
    
    print("Sistema inicializado com sucesso!")

def remover_inimigo(id_instancia):
    """Remove um inimigo específico (usado após combate)"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM InstanciaInimigo WHERE id_instancia = %s;", (id_instancia,))
            if cur.rowcount > 0:
                conn.commit()
                print(f"Inimigo {id_instancia} removido com sucesso!")
                return True
            else:
                print(f"Inimigo {id_instancia} não encontrado.")
                return False
                
    except Exception as e:
        print(f"Erro ao remover inimigo: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def limpar_todos_inimigos_pc(id_pc):
    """Remove todos os inimigos de um PC específico"""
    conn = get_connection()
    if not conn:
        print("Erro: Não foi possível conectar ao banco de dados.")
        return False
    
    try:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM InstanciaInimigo WHERE id_pc = %s;", (id_pc,))
            quantidade_removida = cur.rowcount
            conn.commit()
            
            if quantidade_removida > 0:
                print(f"{quantidade_removida} inimigo(s) removido(s) do PC #{id_pc}.")
            else:
                print(f"Nenhum inimigo encontrado no PC #{id_pc}.")
            
            return True
            
    except Exception as e:
        print(f"Erro ao limpar inimigos: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

# Exemplo de uso
if __name__ == "__main__":
    print("=== SISTEMA DE INIMIGOS ===")
    print("Inicializando...")
    inicializar_sistema()
    
    print("\nTeste do sistema:")
    print("1. Acessando PC normal (ID: 1)")
    acessar_pc(1)
    
    print("\n2. Acessando PC especial")
    acessar_pc(PC_ESPECIAL_ID)
