import random
from datetime import datetime, timedelta
from database import get_location_details, get_pcs_in_room, get_sala_id, call_db_function
import time

def exibir_local(personagem_id, personagem_nome):
    location_details = get_location_details(personagem_id)
    if not location_details:
        print("Erro ao carregar o local. Voltando ao menu.")
        time.sleep(3)
        return False
    nome_local, descricao, _ = location_details
    print(f"--- {personagem_nome} ---")
    print(f"Você está no: {nome_local}")
    print(descricao)
    print("\n--------------------")
    print("O que você faz?\n")
    return True

def montar_opcoes(personagem_id):
    location_details = get_location_details(personagem_id)
    saidas_disponiveis = location_details[2] if location_details else []
    sala_id = get_sala_id(personagem_id)
    pcs = get_pcs_in_room(sala_id)
    opcoes = []
    for saida in saidas_disponiveis:
        opcoes.append({'tipo': 'saida', 'nome': saida})
    if pcs:
        opcoes.append({'tipo': 'pc', 'nome': 'Acessar PC'})
    opcoes.append({'tipo': 'sair', 'nome': 'Voltar ao menu principal'})
    return opcoes, saidas_disponiveis

def exibir_opcoes(opcoes):
    for idx, opcao in enumerate(opcoes, start=1):
        print(f"  [{idx}] {opcao['nome']}")
    print("\n--------------------")

def processar_escolha(personagem_id, opcoes, saidas):
    try:
        escolha_str = input("Sua escolha: ").strip()
        if not escolha_str:
            return True
        escolha_num = int(escolha_str)
        if not (1 <= escolha_num <= len(opcoes)):
            print("\nOpção inválida. Tente novamente.")
            time.sleep(2)
            return True
        opcao = opcoes[escolha_num - 1]
        if opcao['tipo'] == 'saida':
            direcao_escolhida = opcao['nome']
            print(f"\nTentando: {direcao_escolhida}...")
            call_db_function('mover_personagem', personagem_id, direcao_escolhida)
            time.sleep(1)
            return True
        elif opcao['tipo'] == 'pc':
            acessar_pc(personagem_id)
            input("\nPressione Enter para continuar...")
            return True
        elif opcao['tipo'] == 'sair':
            print("\nVoltando ao menu principal...")
            time.sleep(2)
            return False
    except ValueError:
        print("\nPor favor, digite um número. Tente novamente.")
        time.sleep(2)
        return True

def acessar_pc(personagem_id):
    sala_id = get_sala_id(personagem_id)
    pcs = get_pcs_in_room(sala_id)
    if not pcs:
        print("Não há computadores nesta sala.")
        return
    print("\nComputadores disponíveis nesta sala:")
    for i, (id_pc,) in enumerate(pcs, start=1):
        print(f"  [{i}] PC #{id_pc}")
    print("  [0] Cancelar")
    try:
        escolha = int(input("Escolha um PC para acessar: ").strip())
        if escolha == 0:
            return
        if 1 <= escolha <= len(pcs):
            id_pc_escolhido = pcs[escolha - 1][0]
            print(f"\nVocê acessou o PC #{id_pc_escolhido}.")
        else:
            print("Opção inválida.")
    except ValueError:
        print("Digite um número válido.")


def gerar_inimigos_no_pc(id_pc):
    conn = get_connection()
    try:
        with conn.cursor() as cur:
            # Busca o último acesso
            cur.execute("SELECT ultimo_acesso, id_sala FROM PC WHERE id_pc = %s;", (id_pc,))
            row = cur.fetchone()
            if not row:
                print("PC não encontrado.")
                return
            ultimo_acesso, id_sala = row
            agora = datetime.now()
            if not ultimo_acesso:
                ultimo_acesso = agora - timedelta(minutes=5)
            # Calcula quantos períodos de 5 minutos se passaram
            minutos = (agora - ultimo_acesso).total_seconds() // 60
            novos = int(minutos // 5)
            if novos > 0:
                # Busca ids de inimigos possíveis
                cur.execute("SELECT id_inimigo FROM Inimigo;")
                inimigos = [row[0] for row in cur.fetchall()]
                for _ in range(novos):
                    id_inimigo = random.choice(inimigos)
                    vida = random.randint(50, 200)
                    velocidade = random.randint(50, 200)
                    cur.execute(
                        "INSERT INTO InstanciaInimigo (id_inimigo, vida, velocidade, id_sala) VALUES (%s, %s, %s, %s);",
                        (id_inimigo, vida, velocidade, id_sala)
                    )
                print(f"{novos} inimigo(s) apareceram neste PC desde o último acesso!")
            else:
                print("Nenhum novo inimigo apareceu desde o último acesso.")
    finally:
        conn.close()