#!/usr/bin/env python3
"""
Exemplo de uso das funções de inimigos
Este arquivo demonstra como usar todas as funcionalidades do sistema de inimigos.
"""

from funcoes import (
    inicializar_sistema,
    acessar_pc,
    verificar_inimigos_no_pc,
    gerar_inimigo_no_pc,
    limpar_pc_especial,
    remover_inimigo,
    limpar_todos_inimigos_pc,
    PC_ESPECIAL_ID,
    INIMIGOS_CONFIG
)

def menu_principal():
    """Menu principal para testar as funcionalidades"""
    while True:
        print("\n" + "="*50)
        print("SISTEMA DE INIMIGOS - MENU DE TESTE")
        print("="*50)
        print("1. Inicializar sistema")
        print("2. Acessar PC normal")
        print("3. Acessar PC especial")
        print("4. Verificar inimigos em PC")
        print("5. Gerar inimigo específico")
        print("6. Limpar PC especial")
        print("7. Remover inimigo específico")
        print("8. Limpar todos inimigos de um PC")
        print("9. Mostrar configurações dos inimigos")
        print("0. Sair")
        print("="*50)
        
        try:
            opcao = int(input("Escolha uma opção: ").strip())
            
            if opcao == 0:
                print("Saindo do sistema...")
                break
            elif opcao == 1:
                inicializar_sistema()
            elif opcao == 2:
                testar_pc_normal()
            elif opcao == 3:
                acessar_pc(PC_ESPECIAL_ID)
            elif opcao == 4:
                testar_verificar_inimigos()
            elif opcao == 5:
                testar_gerar_inimigo_especifico()
            elif opcao == 6:
                limpar_pc_especial()
            elif opcao == 7:
                testar_remover_inimigo()
            elif opcao == 8:
                testar_limpar_pc()
            elif opcao == 9:
                mostrar_configuracoes()
            else:
                print("Opção inválida!")
                
        except ValueError:
            print("Por favor, digite um número válido!")
        except KeyboardInterrupt:
            print("\nSaindo do sistema...")
            break
        
        input("\nPressione Enter para continuar...")

def testar_pc_normal():
    """Testa o acesso a um PC normal"""
    try:
        id_pc = int(input("Digite o ID do PC para acessar (1-8): ").strip())
        if 1 <= id_pc <= 8:
            acessar_pc(id_pc)
        else:
            print("ID de PC inválido. Use valores entre 1 e 8.")
    except ValueError:
        print("Digite um número válido!")

def testar_verificar_inimigos():
    """Testa a verificação de inimigos em um PC"""
    try:
        id_pc = int(input("Digite o ID do PC para verificar inimigos: ").strip())
        verificar_inimigos_no_pc(id_pc)
    except ValueError:
        print("Digite um número válido!")

def testar_gerar_inimigo_especifico():
    """Testa a geração de um inimigo específico"""
    try:
        id_pc = int(input("Digite o ID do PC: ").strip())
        
        print("\nTipos de inimigos disponíveis:")
        for id_inimigo, config in INIMIGOS_CONFIG.items():
            print(f"{id_inimigo}. {config['nome']} (Chance: {config['chance']}%)")
        
        id_inimigo = int(input("Digite o ID do inimigo (ou 0 para aleatório): ").strip())
        
        if id_inimigo == 0:
            gerar_inimigo_no_pc(id_pc)
        elif id_inimigo in INIMIGOS_CONFIG:
            gerar_inimigo_no_pc(id_pc, id_inimigo)
        else:
            print("ID de inimigo inválido!")
            
    except ValueError:
        print("Digite um número válido!")

def testar_remover_inimigo():
    """Testa a remoção de um inimigo específico"""
    try:
        id_pc = int(input("Digite o ID do PC para ver inimigos: ").strip())
        inimigos = verificar_inimigos_no_pc(id_pc)
        
        if inimigos:
            id_instancia = int(input("Digite o ID da instância do inimigo para remover: ").strip())
            remover_inimigo(id_instancia)
        else:
            print("Nenhum inimigo para remover.")
            
    except ValueError:
        print("Digite um número válido!")

def testar_limpar_pc():
    """Testa a limpeza de todos os inimigos de um PC"""
    try:
        id_pc = int(input("Digite o ID do PC para limpar: ").strip())
        confirmacao = input(f"Tem certeza que deseja remover TODOS os inimigos do PC #{id_pc}? (s/n): ").strip().lower()
        
        if confirmacao in ['s', 'sim', 'y', 'yes']:
            limpar_todos_inimigos_pc(id_pc)
        else:
            print("Operação cancelada.")
            
    except ValueError:
        print("Digite um número válido!")

def mostrar_configuracoes():
    """Mostra as configurações dos inimigos"""
    print("\n" + "="*60)
    print("CONFIGURAÇÕES DOS INIMIGOS")
    print("="*60)
    
    for id_inimigo, config in INIMIGOS_CONFIG.items():
        print(f"\n{id_inimigo}. {config['nome']}")
        print(f"   Chance de spawn: {config['chance']}%")
        print(f"   Vida: {config['vida_min']} - {config['vida_max']}")
        print(f"   Velocidade: {config['velocidade_min']} - {config['velocidade_max']}")
    
    print(f"\nPC Especial ID: {PC_ESPECIAL_ID}")
    print("- Não gera inimigos automaticamente")
    print("- Use 'Limpar PC' para gerar 1-8 inimigos aleatórios")

def demonstracao_completa():
    """Executa uma demonstração completa do sistema"""
    print("\n" + "="*60)
    print("DEMONSTRAÇÃO COMPLETA DO SISTEMA")
    print("="*60)
    
    print("\n1. Inicializando sistema...")
    inicializar_sistema()
    
    print("\n2. Verificando PC especial...")
    verificar_inimigos_no_pc(PC_ESPECIAL_ID)
    
    print("\n3. Gerando inimigos no PC especial...")
    limpar_pc_especial()
    
    print("\n4. Verificando inimigos gerados...")
    verificar_inimigos_no_pc(PC_ESPECIAL_ID)
    
    print("\n5. Testando PC normal (ID: 1)...")
    acessar_pc(1)
    
    print("\n6. Gerando um Bug específico no PC 1...")
    gerar_inimigo_no_pc(1, 1)  # Gera um Bug
    
    print("\n7. Verificando PC 1...")
    verificar_inimigos_no_pc(1)
    
    print("\nDemonstração concluída!")

if __name__ == "__main__":
    print("SISTEMA DE INIMIGOS - EXEMPLO DE USO")
    print("Escolha uma opção:")
    print("1. Menu interativo")
    print("2. Demonstração automática")
    
    try:
        opcao = int(input("Sua escolha: ").strip())
        if opcao == 1:
            menu_principal()
        elif opcao == 2:
            demonstracao_completa()
        else:
            print("Opção inválida!")
    except ValueError:
        print("Digite um número válido!")
    except KeyboardInterrupt:
        print("\nSaindo...")
